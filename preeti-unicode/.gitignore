# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
test_env/

# Test files
test_*.txt
test_*.pdf
test_*.docx
test_*.html
*_converted.*
sample_*

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Log files
*.log

# IDE
.idea/
.vscode/

# Cache directories
.cache/
.pytest_cache/

# UV specific
.uv/
